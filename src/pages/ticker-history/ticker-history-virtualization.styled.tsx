import styled from 'styled-components'
import { Table } from '@mz-codes/design-system'

export const VirtualScrollContainer = styled.div<{ maxHeight: number }>`
  max-height: ${({ maxHeight }) => maxHeight}px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
`

export const VirtualContentContainer = styled.div<{ totalHeight: number }>`
  height: ${({ totalHeight }) => totalHeight}px;
  position: relative;
`

export const VirtualItemsWrapper = styled.div<{ translateY: number }>`
  transform: translateY(${({ translateY }) => translateY}px);
`

export const VirtualizedTableRow = styled(Table.TR) <{ itemHeight: number }>`
  height: ${({ itemHeight }) => itemHeight}px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
`

export const VirtualizedTable = styled.table<{ scrollbarWidth?: number }>`
  width: ${({ scrollbarWidth = 0 }) => `calc(100% - ${scrollbarWidth}px)`};
  table-layout: fixed;
  border-collapse: collapse;

  td {
    padding: 20px;
    text-align: left;
    font-size: 14px;
    font-weight: 500;
    color: inherit;
    vertical-align: middle;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
`

export const CenteredLoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  width: 100%;
`

// Container para sincronizar larguras das colunas
export const TableHeaderWrapper = styled.div<{ scrollbarWidth?: number }>`
  width: 100%;
  position: relative;

  table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
  }

  /* Ajusta a largura da tabela para compensar a barra de rolagem */
  ${({ scrollbarWidth = 0 }) =>
    scrollbarWidth > 0 &&
    `
    table {
      width: calc(100% - ${scrollbarWidth}px);
    }
  `}
`

// Wrapper para a tabela principal que detecta se precisa de scroll
export const VirtualizedTableWrapper = styled.div`
  position: relative;
  width: 100%;
  overflow: hidden;
`
