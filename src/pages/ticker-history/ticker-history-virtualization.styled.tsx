import styled from 'styled-components'
import { Table } from '@mz-codes/design-system'

export const VirtualScrollContainer = styled.div<{ maxHeight: number }>`
  max-height: ${({ maxHeight }) => maxHeight}px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  margin-top: 0;
  padding-top: 0;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
`

export const VirtualContentContainer = styled.div<{ totalHeight: number }>`
  height: ${({ totalHeight }) => totalHeight}px;
  position: relative;
`

export const VirtualItemsWrapper = styled.div<{ translateY: number }>`
  transform: translateY(${({ translateY }) => translateY}px);
`

export const VirtualizedTableRow = styled(Table.TR) <{ itemHeight: number }>`
  height: ${({ itemHeight }) => itemHeight}px;
  border-bottom: none;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 1px;
    background-color: #344b69;
  }

  &:hover {
    background: radial-gradient(
      52.96% 3704.62% at 50% 50%,
      rgba(35, 40, 56, 0.75) 59.9%,
      rgba(35, 40, 56, 0.376) 80.21%,
      rgba(35, 40, 56, 0) 100%
    );
  }
`

export const VirtualizedTable = styled.table<{ scrollbarWidth?: number }>`
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin: 0;
  padding: 0;

  tbody {
    margin: 0;
    padding: 0;
  }

  td {
    padding: 20px;
    text-align: left;
    font-size: 14px;
    font-weight: 500;
    color: inherit;
    vertical-align: middle;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  /* Ajuste específico para a segunda coluna (Opening price) */
  td:nth-child(2) {
    padding-left: 18px;
  }
`

export const CenteredLoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  width: 100%;
`

export const TableHeaderWrapper = styled.div<{ scrollbarWidth?: number }>`
  width: 100%;
  position: relative;
  margin-bottom: -1px; /* Remove espaço entre cabeçalho e corpo */

  table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
  }

  ${({ scrollbarWidth = 0 }) =>
    scrollbarWidth > 0 &&
    `
    margin-right: ${scrollbarWidth}px;
  `}
`

export const VirtualizedTableWrapper = styled.div`
  position: relative;
  width: 100%;
  overflow: hidden;
`
