import { useEffect, useRef, useState } from 'react'
import { Table } from '@mz-codes/design-system'
import { useVirtualization } from '../ticker-history-virtualization.hook'
import {
  VirtualScrollContainer,
  VirtualContentContainer,
  VirtualItemsWrapper,
  VirtualizedTableRow,
  VirtualizedTable,
  TableHeaderWrapper,
} from '../ticker-history-virtualization.styled'
import { VirtualizedTableWithHeaderProps } from '../ticker-history.types'

export function VirtualizedTableWithHeader({
  items,
  itemHeight = 45,
  maxVisibleItems = 20,
  translations,
}: VirtualizedTableWithHeaderProps) {
  const virtualization = useVirtualization(items, { itemHeight, maxVisibleItems })
  const { state, actions } = virtualization
  const [scrollbarWidth, setScrollbarWidth] = useState(0)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const detectScrollbarWidth = () => {
      if (!scrollContainerRef.current) return

      const container = scrollContainerRef.current
      const hasVerticalScrollbar = container.scrollHeight > container.clientHeight

      if (hasVerticalScrollbar) {
        const detectedScrollbarWidth = container.offsetWidth - container.clientWidth
        setScrollbarWidth(detectedScrollbarWidth)
      } else {
        setScrollbarWidth(0)
      }
    }

    const timeoutId = setTimeout(detectScrollbarWidth, 150)

    const resizeObserver = new ResizeObserver(() => {
      setTimeout(detectScrollbarWidth, 50)
    })

    if (scrollContainerRef.current) {
      resizeObserver.observe(scrollContainerRef.current)
    }

    const mutationObserver = new MutationObserver(() => {
      setTimeout(detectScrollbarWidth, 50)
    })

    if (scrollContainerRef.current) {
      mutationObserver.observe(scrollContainerRef.current, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style'],
      })
    }

    return () => {
      clearTimeout(timeoutId)
      resizeObserver.disconnect()
      mutationObserver.disconnect()
    }
  }, [state.shouldVirtualize, items.length])

  if (!state.shouldVirtualize) {
    return (
      <Table>
        <Table.THead>
          <Table.TR>
            <Table.TH>{translations.tableTitleDate}</Table.TH>
            <Table.TH>{translations.tableTitleOpeningPrice}</Table.TH>
            <Table.TH>{translations.tableTitleClosingPrice}</Table.TH>
            <Table.TH>{translations.tableTitleLowPrice}</Table.TH>
            <Table.TH>{translations.tableTitleHighPrice}</Table.TH>
            <Table.TH>{translations.tableTitleVolume}</Table.TH>
          </Table.TR>
        </Table.THead>
        <Table.TBody>
          {items.map((tickerPrice, index) => {
            const { date, high, low, open, close, volume } = tickerPrice
            const key = `${date}-${index}`
            return (
              <Table.TR key={key}>
                <Table.TD>{date}</Table.TD>
                <Table.TD>{open}</Table.TD>
                <Table.TD>{close}</Table.TD>
                <Table.TD>{low}</Table.TD>
                <Table.TD>{high}</Table.TD>
                <Table.TD>{volume}</Table.TD>
              </Table.TR>
            )
          })}
        </Table.TBody>
      </Table>
    )
  }

  return (
    <div style={{ position: 'relative', width: '100%', margin: 0, padding: 0 }}>
      <TableHeaderWrapper scrollbarWidth={scrollbarWidth}>
        <Table>
          <Table.THead>
            <Table.TR>
              <Table.TH>{translations.tableTitleDate}</Table.TH>
              <Table.TH>{translations.tableTitleOpeningPrice}</Table.TH>
              <Table.TH>{translations.tableTitleClosingPrice}</Table.TH>
              <Table.TH>{translations.tableTitleLowPrice}</Table.TH>
              <Table.TH>{translations.tableTitleHighPrice}</Table.TH>
              <Table.TH>{translations.tableTitleVolume}</Table.TH>
            </Table.TR>
          </Table.THead>
        </Table>
      </TableHeaderWrapper>

      <VirtualScrollContainer
        ref={scrollContainerRef}
        maxHeight={state.containerMaxHeight}
        onScroll={actions.handleScroll}
      >
        <VirtualContentContainer totalHeight={state.contentTotalHeight}>
          <VirtualItemsWrapper translateY={state.itemsWrapperTranslateY}>
            <VirtualizedTable scrollbarWidth={scrollbarWidth}>
              <tbody>
                {state.visibleItems.map((tickerPrice, index) => {
                  const { date, high, low, open, close, volume } = tickerPrice
                  const key = `${date}-${index}`
                  return (
                    <VirtualizedTableRow key={key} itemHeight={itemHeight}>
                      <td>{date}</td>
                      <td>{open}</td>
                      <td>{close}</td>
                      <td>{low}</td>
                      <td>{high}</td>
                      <td>{volume}</td>
                    </VirtualizedTableRow>
                  )
                })}
              </tbody>
            </VirtualizedTable>
          </VirtualItemsWrapper>
        </VirtualContentContainer>
      </VirtualScrollContainer>
    </div>
  )
}
