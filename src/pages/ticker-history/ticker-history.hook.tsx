import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { subDays } from 'date-fns'
import { getCompany } from 'globals/storages/locals'
import { getTickers, ticker } from 'globals/services/tickers'
import { getSerializedTickerPriceHistory, TPriceHistory, postTickerPriceHistoryExport } from 'hooks'
import { GoToHistoryButton } from 'components'
import { TOption, useToast } from '@mz-codes/design-system'
import { i18n } from 'translate'
import { formatDateToString } from 'utils'
import { BaseError, SelectedTickerNotFound } from 'errors'
import { TTickerHistoryHookReturn } from './ticker-history.types'

const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
const MAX_CACHE_SIZE = 20

interface CacheEntry {
  data: TPriceHistory[]
  timestamp: number
  lastAccessed: number
}

class LRUCache {
  private cache = new Map<string, CacheEntry>()

  private maxSize: number

  constructor(maxSize: number = MAX_CACHE_SIZE) {
    this.maxSize = maxSize
  }

  get(key: string): CacheEntry | undefined {
    const entry = this.cache.get(key)
    if (entry) {
      entry.lastAccessed = Date.now()
      return entry
    }
    return undefined
  }

  set(key: string, value: Omit<CacheEntry, 'lastAccessed'>): void {
    const entry: CacheEntry = {
      ...value,
      lastAccessed: Date.now(),
    }

    this.cache.set(key, entry)

    if (this.cache.size > this.maxSize) {
      this.evictLeastRecentlyUsed()
    }
  }

  private evictLeastRecentlyUsed(): void {
    const entries = Array.from(this.cache.entries())
    const oldestEntry = entries.reduce(
      (oldest, [key, entry]) => {
        if (entry.lastAccessed < oldest.lastAccessed) {
          return { key, lastAccessed: entry.lastAccessed }
        }
        return oldest
      },
      { key: '', lastAccessed: Infinity }
    )

    if (oldestEntry.key) {
      this.cache.delete(oldestEntry.key)
    }
  }

  clear(): void {
    this.cache.clear()
  }

  cleanup(): void {
    const now = Date.now()
    const expiredKeys = Array.from(this.cache.entries())
      .filter(([, entry]) => now - entry.timestamp > CACHE_DURATION)
      .map(([key]) => key)

    expiredKeys.forEach((key) => this.cache.delete(key))
  }

  size(): number {
    return this.cache.size
  }
}

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  const timerRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (timerRef.current) clearTimeout(timerRef.current)
    timerRef.current = setTimeout(() => setDebouncedValue(value), delay)
    return () => {
      if (timerRef.current) clearTimeout(timerRef.current)
    }
  }, [value, delay])

  return debouncedValue
}

export function useTickerHistory(): TTickerHistoryHookReturn {
  const company = getCompany()
  const currentLanguage = i18n.language.startsWith('pt') ? 'pt-BR' : 'en-US'
  const startDate = subDays(new Date(), 7)
  const endDate = subDays(new Date(), 1)

  const cacheRef = useRef<LRUCache>(new LRUCache())
  const lastCompanyIdRef = useRef<string>(company.id)

  useEffect(() => {
    if (lastCompanyIdRef.current !== company.id) {
      cacheRef.current.clear()
      lastCompanyIdRef.current = company.id
    }
  }, [company.id])

  useEffect(() => {
    const interval = setInterval(() => {
      cacheRef.current.cleanup()
    }, CACHE_DURATION)

    return () => clearInterval(interval)
  }, [])

  const { createToast } = useToast()
  const [tickers, setTickers] = useState<ticker[]>([])
  const [currentTicker, setCurrentTicker] = useState<TOption>()
  const [tickerOptions, setTickerOptions] = useState<TOption[]>()
  const [selectedStartDate, setSelectedStartDate] = useState(startDate)
  const [selectedEndDate, setSelectedEndDate] = useState(endDate)
  const [listItems, setListItems] = useState<TPriceHistory[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const selectedTicker = useMemo(
    () => tickers.find((tickerParam) => tickerParam.tickerId === currentTicker?.value),
    [tickers, currentTicker?.value]
  )

  const debouncedStartDate = useDebounce(selectedStartDate, 500)
  const debouncedEndDate = useDebounce(selectedEndDate, 500)

  const handleGetTickers = useCallback(async () => {
    const response = await getTickers(company.id)

    const tickersFiltered = response?.filter((item) => item.label !== 'TOTAL')

    const options = tickersFiltered.map<TOption>((item) => ({
      label: item.label,
      value: item.tickerId,
    }))
    setTickers(tickersFiltered)
    setCurrentTicker(options[0])
    setTickerOptions(options)
  }, [company.id])

  useEffect(() => {
    handleGetTickers()
  }, [handleGetTickers])

  const handleLoadTickerPriceHistory = useCallback(async () => {
    setIsLoading(true)
    try {
      if (!selectedTicker) throw new SelectedTickerNotFound()

      const cacheKey = `${selectedTicker.xigniteTicker}-${formatDateToString(debouncedStartDate)}-${formatDateToString(debouncedEndDate)}`
      const cachedData = cacheRef.current.get(cacheKey)
      const now = Date.now()

      if (cachedData && now - cachedData.timestamp < CACHE_DURATION) {
        setListItems(cachedData.data)
        setIsLoading(false)
        return
      }

      const data = await getSerializedTickerPriceHistory({
        ticker: selectedTicker.xigniteTicker,
        startDate: formatDateToString(debouncedStartDate),
        endDate: formatDateToString(debouncedEndDate),
      })

      cacheRef.current.set(cacheKey, { data, timestamp: now })

      setListItems(data)
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          duration: 20000,
          type: 'error',
        })
        return
      }
      createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        duration: 20000,
        type: 'error',
      })
    } finally {
      setIsLoading(false)
    }
  }, [createToast, debouncedEndDate, debouncedStartDate, selectedTicker])

  useEffect(() => {
    if (!currentTicker?.value) return
    handleLoadTickerPriceHistory()
  }, [currentTicker, debouncedStartDate, debouncedEndDate, handleLoadTickerPriceHistory])

  const handleTickerValue = (selectedTickerParam: TOption) => {
    if (currentTicker?.value === selectedTickerParam.value) return
    setCurrentTicker(selectedTickerParam)
  }

  const handleStartDateValue = useCallback(
    (value: Date | null) => {
      if (!value || value === selectedStartDate) return
      setSelectedStartDate(value)
    },
    [selectedStartDate]
  )

  const handleEndDateValue = useCallback(
    (value: Date | null) => {
      if (!value || value === selectedEndDate) return
      setSelectedEndDate(value)
    },
    [selectedEndDate]
  )

  const onExportClick = async () => {
    try {
      if (!selectedTicker) throw new SelectedTickerNotFound()

      await postTickerPriceHistoryExport({
        companyId: company.id,
        companyName: company.displayName,
        tickerId: selectedTicker.tickerId,
        ticker: selectedTicker.xigniteTicker,
        stockType: selectedTicker.label.toString(),
        startDate: formatDateToString(selectedStartDate),
        endDate: formatDateToString(selectedEndDate),
        language: currentLanguage,
      })
      createToast({
        title: i18n.t('tickerPriceHistory.success'),
        description: i18n.t('tickerPriceHistory.exportMessage'),
        duration: 9000,
        type: 'success',
        buttons: <GoToHistoryButton />,
      })
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        return
      }
      createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        duration: 9000,
        type: 'error',
      })
    }
  }

  return {
    tickers,
    currentTicker,
    tickerOptions,
    listItems,
    selectedStartDate,
    selectedEndDate,
    isLoading,
    currentLanguage,
    handleTickerValue,
    handleStartDateValue,
    handleEndDateValue,
    onExportClick,
  }
}
