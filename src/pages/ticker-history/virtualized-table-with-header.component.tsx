import React, { useEffect, useRef, useState } from 'react'
import { Table } from '@mz-codes/design-system'
import { TPriceHistory } from 'hooks'
import { useVirtualization } from './ticker-history-virtualization.hook'
import {
  VirtualScrollContainer,
  VirtualContentContainer,
  VirtualItemsWrapper,
  VirtualizedTableRow,
  VirtualizedTable,
  TableHeaderWrapper,
} from './ticker-history-virtualization.styled'

interface VirtualizedTableWithHeaderProps {
  items: TPriceHistory[]
  itemHeight?: number
  maxVisibleItems?: number
  translations: {
    tableTitleDate: string
    tableTitleOpeningPrice: string
    tableTitleClosingPrice: string
    tableTitleLowPrice: string
    tableTitleHighPrice: string
    tableTitleVolume: string
  }
}

export function VirtualizedTableWithHeader({
  items,
  itemHeight = 45,
  maxVisibleItems = 20,
  translations,
}: VirtualizedTableWithHeaderProps) {
  const virtualization = useVirtualization(items, { itemHeight, maxVisibleItems })
  const { state, actions } = virtualization
  const [scrollbarWidth, setScrollbarWidth] = useState(0)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  // Detecta a largura da barra de rolagem
  useEffect(() => {
    const detectScrollbarWidth = () => {
      if (!scrollContainerRef.current) return

      const container = scrollContainerRef.current
      const hasVerticalScrollbar = container.scrollHeight > container.clientHeight

      if (hasVerticalScrollbar) {
        // Calcula a largura da barra de rolagem
        const scrollbarWidth = container.offsetWidth - container.clientWidth
        setScrollbarWidth(scrollbarWidth)
      } else {
        setScrollbarWidth(0)
      }
    }

    // Detecta inicialmente após um pequeno delay para garantir que o DOM está renderizado
    const timeoutId = setTimeout(detectScrollbarWidth, 150)

    // Observa mudanças no container
    const resizeObserver = new ResizeObserver(() => {
      // Adiciona um pequeno delay para garantir que as mudanças foram aplicadas
      setTimeout(detectScrollbarWidth, 50)
    })

    if (scrollContainerRef.current) {
      resizeObserver.observe(scrollContainerRef.current)
    }

    // Detecta mudanças quando o conteúdo muda
    const mutationObserver = new MutationObserver(() => {
      setTimeout(detectScrollbarWidth, 50)
    })

    if (scrollContainerRef.current) {
      mutationObserver.observe(scrollContainerRef.current, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style']
      })
    }

    return () => {
      clearTimeout(timeoutId)
      resizeObserver.disconnect()
      mutationObserver.disconnect()
    }
  }, [state.shouldVirtualize, items.length])

  // Renderização sem virtualização para poucos itens
  if (!state.shouldVirtualize) {
    return (
      <Table>
        <Table.THead>
          <Table.TR>
            <Table.TH>{translations.tableTitleDate}</Table.TH>
            <Table.TH>{translations.tableTitleOpeningPrice}</Table.TH>
            <Table.TH>{translations.tableTitleClosingPrice}</Table.TH>
            <Table.TH>{translations.tableTitleLowPrice}</Table.TH>
            <Table.TH>{translations.tableTitleHighPrice}</Table.TH>
            <Table.TH>{translations.tableTitleVolume}</Table.TH>
          </Table.TR>
        </Table.THead>
        <Table.TBody>
          {items.map((tickerPrice, index) => {
            const { date, high, low, open, close, volume } = tickerPrice
            const key = `${date}-${index}`
            return (
              <Table.TR key={key}>
                <Table.TD>{date}</Table.TD>
                <Table.TD>{open}</Table.TD>
                <Table.TD>{close}</Table.TD>
                <Table.TD>{low}</Table.TD>
                <Table.TD>{high}</Table.TD>
                <Table.TD>{volume}</Table.TD>
              </Table.TR>
            )
          })}
        </Table.TBody>
      </Table>
    )
  }

  // Renderização com virtualização
  return (
    <div style={{ position: 'relative', width: '100%' }}>
      {/* Cabeçalho da tabela com ajuste para barra de rolagem */}
      <TableHeaderWrapper scrollbarWidth={scrollbarWidth}>
        <Table>
          <Table.THead>
            <Table.TR>
              <Table.TH>{translations.tableTitleDate}</Table.TH>
              <Table.TH>{translations.tableTitleOpeningPrice}</Table.TH>
              <Table.TH>{translations.tableTitleClosingPrice}</Table.TH>
              <Table.TH>{translations.tableTitleLowPrice}</Table.TH>
              <Table.TH>{translations.tableTitleHighPrice}</Table.TH>
              <Table.TH>{translations.tableTitleVolume}</Table.TH>
            </Table.TR>
          </Table.THead>
        </Table>
      </TableHeaderWrapper>

      {/* Corpo virtualizado da tabela */}
      <VirtualScrollContainer
        ref={scrollContainerRef}
        maxHeight={state.containerMaxHeight}
        onScroll={actions.handleScroll}
      >
        <VirtualContentContainer totalHeight={state.contentTotalHeight}>
          <VirtualItemsWrapper translateY={state.itemsWrapperTranslateY}>
            <VirtualizedTable scrollbarWidth={scrollbarWidth}>
              <tbody>
                {state.visibleItems.map((tickerPrice, index) => {
                  const { date, high, low, open, close, volume } = tickerPrice
                  const key = `${date}-${index}`
                  return (
                    <VirtualizedTableRow key={key} itemHeight={itemHeight}>
                      <td>{date}</td>
                      <td>{open}</td>
                      <td>{close}</td>
                      <td>{low}</td>
                      <td>{high}</td>
                      <td>{volume}</td>
                    </VirtualizedTableRow>
                  )
                })}
              </tbody>
            </VirtualizedTable>
          </VirtualItemsWrapper>
        </VirtualContentContainer>
      </VirtualScrollContainer>
    </div>
  )
}
