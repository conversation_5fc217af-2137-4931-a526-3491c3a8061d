import { Buttons, NewDatepicker, Dropdown, Loading, Header } from '@mz-codes/design-system'
import { Page, PageContent, DataNotFound } from 'components'
import { i18n } from 'translate'
import { TTickerHistoryTemplate } from './ticker-history.types'
import { CenteredLoadingContainer } from './ticker-history-virtualization.styled'
import { VirtualizedTableWithHeader } from './virtualized-table-with-header.component'



export function TickerHistoryTemplate(props: TTickerHistoryTemplate) {
  const {
    translations,
    handleTickerValue,
    currentTicker,
    tickerOptions = [],
    handleStartDateValue,
    selectedStartDate,
    selectedEndDate,
    handleEndDateValue,
    onExportClick,
    isLoading,
    listItems,
  } = props

  return (
    <Page>
      <Header>
        <Header.Content>
          <Header.Item>
            <Header.Label>{translations.tickerLabel}</Header.Label>
            <Dropdown options={tickerOptions} selected={currentTicker} onChange={handleTickerValue} />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.startDateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={selectedStartDate}
              onChange={handleStartDateValue}
              maxDate={selectedEndDate}
              hint={i18n.t('startDate')}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.endDateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={selectedEndDate}
              onChange={handleEndDateValue}
              minDate={selectedStartDate}
              hint={i18n.t('endDate')}
            />
          </Header.Item>
        </Header.Content>
        <Header.ButtonGroup style={{ justifyContent: 'center' }}>
          <Buttons.Export onClick={onExportClick}>{translations.exportButton as string}</Buttons.Export>
        </Header.ButtonGroup>
      </Header>
      <PageContent>
        {isLoading && (
          <CenteredLoadingContainer>
            <Loading />
          </CenteredLoadingContainer>
        )}
        {!isLoading && (
          <>
            {listItems.length === 0 && <DataNotFound>{translations.dataNotFound}</DataNotFound>}
            {listItems.length !== 0 && (
              <VirtualizedTableWithHeader
                items={listItems}
                itemHeight={45}
                maxVisibleItems={20}
                translations={{
                  tableTitleDate: translations.tableTitleDate as string,
                  tableTitleOpeningPrice: translations.tableTitleOpeningPrice as string,
                  tableTitleClosingPrice: translations.tableTitleClosingPrice as string,
                  tableTitleLowPrice: translations.tableTitleLowPrice as string,
                  tableTitleHighPrice: translations.tableTitleHighPrice as string,
                  tableTitleVolume: translations.tableTitleVolume as string,
                }}
              />
            )}
          </>
        )}
      </PageContent>
    </Page>
  )
}
